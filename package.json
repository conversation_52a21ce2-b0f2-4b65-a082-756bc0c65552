{"name": "fixr-dashboard", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "build-watch": "vite build --watch", "preview": "vite preview"}, "dependencies": {"@heroicons/vue": "^1.0.6", "@sentry/browser": "^8.22.0", "@sentry/vue": "^7.47.0", "@stripe/stripe-js": "^1.50.0", "@vuepic/vue-datepicker": "^11.0.2", "axios": "^1.10.0", "click-outside-vue3": "^4.0.1", "hellosign-embedded": "^2.12.0", "pinia": "^2.0.28", "vue": "^3.5.13", "vue-router": "^4.1.6", "vue3-echarts": "^1.1.0"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "@sentry/vite-plugin": "^2.21.1", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.13", "postcss": "^8.4.21", "tailwindcss": "^3.4.17", "typescript": "^5.3.3", "vite": "^6.0.10", "vite-svg-loader": "^4.0.0", "vue-tsc": "^2.2.0"}}