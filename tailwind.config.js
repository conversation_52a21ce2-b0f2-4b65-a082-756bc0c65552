const defaultTheme = require('tailwindcss/defaultTheme')

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
      "./src/**/*.{js,vue}"
  ],
  safelist: [
    {
      pattern: /grid-cols-\d+/,
      variants: ['sm', 'md', 'lg', 'xl', '2xl'],
    },
  ],
  theme: {
    fontSize: {
      xs: ['0.75rem', '1.08rem'],
      sm: ['0.875rem', '1.28rem'],
      base: ['1rem', '1.45rem'],
      lg: ['1.25rem', '1.55rem'],
      xl: ['1.5rem', '1.64rem'],
      '2xl': ['2rem', '2.24rem'],
      '3xl': ['2.25rem', '2.54rem'],
      '4xl': ['2.5rem', '2.84rem'],
      '5xl': ['3rem', '3.44rem'],
    },
    letterSpacing: {
      tighter: '-.025em',
      tight: '-.010em',
      normal: '0',
      wide: '.025em',
      wider: '.05em',
      widest: '.1em',
    },
    extend: {
      fontFamily: {
        'sans': ['Inter', ...defaultTheme.fontFamily.sans],
      },
      fontSize: {
        'md': '1.15rem',
      },
      colors: {
        blue: {
          '800': '#002850',
          '900': '#00203F',
          'fixr': '#0081FF'
        },
        cyan: {
          '25' : '#F0FAFF',
          '50' : '#E5F6FF',
          '100': '#CCEDFF',
          '200': '#99DAFF',
          '300': '#66C8FF',
          '400': '#33B5FF',
          '500': '#00A3FF',
          '600': '#0082CC',
          '700': '#006299',
          '800': '#004166',
          '900': '#002133',
        }
      },
      boxShadow: {
        'light': '0px 4px 40px 5px rgba(0, 0, 0, 0.03)',
        'card': '0 1px 6px rgba(32, 33, 35, 0.26)',
        'module': '0 1px 2px 0 rgba(0, 0, 0, 0.10)',
      },
      animation: {
        'spin-slow': 'spin 30s linear infinite',
        'spin-2': 'spin 3s linear infinite',
        'expand': 'expand 12s ease-in-out infinite'
      },
      keyframes: {
        expand: {
          '0%, 100%': { transform: 'scaleY(2)' },
          '50%': { transform: 'scaleY(1)' },
        }
      },
      spacing: {
        '104': '26rem',
        '112': '28rem',
        '120': '30rem',
        '128': '32rem',
      }
    },
  },
  plugins: [],
}
