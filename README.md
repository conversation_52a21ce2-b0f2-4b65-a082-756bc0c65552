# fixr-dashboard

This template should help get you started developing Fixr dashboard with Vue 3 / Vite.

## Project Setup

### 1. Clone repo .e.g
```sh
   <NAME_EMAIL>:heapsgoodservices/fixr-dashboard.git
```
   
### 2. Install Node dependencies
 ```sh
    npm install
 ```
   
### 3. Ensure .env is set up correctly, as per .env.example. See <PERSON> or <PERSON> for any keys you need help with.
 ```sh
    # Environment
    VITE_DEBUG=true
    VITE_ENVIRONMENT=development
    VITE_APP_URL=http://localhost:5173
    VITE_APP_NAME=Fixr Dashboard
    
    # API Configuration
    VITE_API_DRIVER=api
    VITE_API_DUMMY_DELAY=250
    VITE_API_BASE_URL=/api
    VITE_API_PROXY_TARGET=https://mydev.admin-sr.com/dashboard-api
    
    # Stripe Configuration
    VITE_STRIPE_PUBLISHABLE_KEY=pk_test_example_key
    
    # Google Configuration
    VITE_GOOGLE_MAPS_API_KEY=example_key
    
    # URL Configuration
    VITE_FIXR_URL=https://exampledev.fixr.com
    
    # Contact Configuration
    VITE_DEFAULT_SUPPORT_EMAIL=<EMAIL>
    VITE_DEFAULT_SUPPORT_PHONE="************"
    VITE_DEFAULT_SUPPORT_MEETING_URL="https://example.meeting.url"
    
    # Watchdog Configuration
    VITE_WATCHDOG_URL="https://api.exampledev.tcpawatchdog.com/"
    VITE_WATCHDOG_PLAYBACK_URL="/v1/playback/"
 ```

### 4. Ensure your Admin2.0 dev remote is up-to-date. During Development the changes can be found in the **A20-1460** epic, until merged in to dev. Ensure current branch is deployed to remove, front end may need to be rebuilt.

### 5. Ensure your SolarReviews legacy dev remote is also up-to-date. The required integration routes can be found in the **SR-6663** epic, until merged in to dev.

### 6. Ensure at least one Company has a CompanyUser set up as a User, with an email and password set, through Admin2.0 - this must be done either through Fixr registration, or an Admin2.0 up-to-date with dashboard routes. CompanyUsers set up in Legacy SR/registration **will not** work in Fixr dashboard. 
   
### 7. Compile & run dev server with HMR
 ```sh
    npm run dev
 ```

## Error Reporting

Use the ErrorStore to report any issues to the user, console, and Sentry. 
The below snippet will create a dismissable modal for the user, displaying an optional message passed as the second parameter. 

The error object will be logged to the console and caught by Sentry.  
If there's no optional message passed, the modal will try to get an error message from the error object.

```js
import { useErrorStore } from "@/stores/errors.js";

const errors = useErrorStore();

try {
   // do something
} catch (error) {
   errors.report(error, "There was an error adding your card, please contact support.");
}

```


## Notes

### Composition API
Fixr dashboard uses Vue 3's Composition API for front end components.
### API endpoints
Fixr dashboard API calls all go to Admin2.0 - if legacy calls are required, they can be made from Admin2.0, not directly from the dashboard.
### Navigation   
Navigation flow and items can be updated through *index.js* and *SideBar.vue*
### Pinia
Fixr Dashboard uses Pinia stores to retain data between views, reducing required API bandwidth when pages are viewed repeatedly. See the */stores* folder for examples