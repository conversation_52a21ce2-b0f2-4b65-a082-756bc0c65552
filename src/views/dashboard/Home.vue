<template>
    <MainGrid>
        <PendingApproval v-if="company.status === 'Pending Approval'" />
        <CampaignBulkActivate />
        <LeadProfitability
            :stores-initialized="profitabilityAssumptionStoreLoaded"
            :initial-load="initialLoading"
            :initial-data="initialProfitabilityStatistics"
        />
        <LeadVolume
            :stores-initialized="profitabilityAssumptionStoreLoaded"
            :initial-load="initialLoading"
            :initial-data="initialVolumeStatistics"
        />
        <LeadTypesPurchased
            :stores-initialized="profitabilityAssumptionStoreLoaded"
        />
        <MoreLeads />

        <template v-slot:side>
            <ProfitabilityAssumptionFields :store-loaded="profitabilityAssumptionStoreLoaded"/>
            <UnpaidPromotion />
            <ProfileProgress />
        </template>
    </MainGrid>
</template>

<script setup>
import MainGrid from "@/components/layouts/MainGrid.vue";
import PendingApproval from "@/components/dashboard/PendingApproval.vue";
import {useCompanyStore} from "@/stores/company";
import CampaignBulkActivate from "@/components/dashboard/CampaignBulkActivate.vue";
import LeadProfitability from "@/components/dashboard/LeadProfitability.vue";
import LeadVolume from "@/components/dashboard/LeadVolume.vue";
import LeadTypesPurchased from "@/components/dashboard/LeadTypesPurchased.vue";
import MoreLeads from "@/components/dashboard/MoreLeads.vue";
import ProfitabilityAssumptionFields from "@/components/dashboard/ProfitabilityAssumptionFields.vue";
import UnpaidPromotion from "@/components/dashboard/UnpaidPromotion.vue";
import ProfileProgress from "@/components/dashboard/ProfileProgress.vue";
import { onBeforeMount, ref } from "vue";
import {useProfitabilityAssumptionsStore} from "@/stores/profitability-assumptions";
import { useServicesStore } from "@/stores/services";

const company = useCompanyStore();
const serviceStore = useServicesStore();
const apiService = serviceStore.apiServiceV4;

const profitabilityAssumption = useProfitabilityAssumptionsStore();
const profitabilityAssumptionStoreLoaded = ref(false);

const initialLoading = ref(true);
const initialized = ref(false);
const initialVolumeStatistics = ref({});
const initialProfitabilityStatistics = ref({});

profitabilityAssumption.initialize().then(() => profitabilityAssumptionStoreLoaded.value = true);

onBeforeMount(() => {
    loadDefaultStatistics();
});

const loadDefaultStatistics = async () => {
    if (initialized.value)
        return;

    initialLoading.value = true;
    const response = await apiService.getDefaultAnalyticsStatistics().catch(e => {
        console.error(e);
    });

    const initialStatistics = response.data?.data?.statistics;

    initialVolumeStatistics.value = initialStatistics.volume ?? null;
    initialProfitabilityStatistics.value = initialStatistics.profitability ?? null;

    initialLoading.value = false;
}

</script>