<template>
    <div class="flex flex-col items-center p-6 md:p-10 gap-6 rounded-xl w-auto md:w-128">
        <Modal v-if="showContractModal"
               @clicked:confirm="getContractForCompany"
               confirm-label="Next"
               :small="true"
               :show-cancel-button="true"
               cancel-label="Logout"
               @clicked:cancel="auth.logout()"
        >
            <template v-slot:header>
                Updated Contract
            </template>
            <template v-slot:body>
                <div>
                    Please agree to our updated Lead Buying Agreement
                </div>
                <div class="pt-4">
                    The agreement will take you to another page, please allow pop ups
                </div>
                <div class="pt-4">
                    Alternatively, this contract will be sent to your email and can be signed from there
                </div>
                <div class="pt-4">
                    If you have just completed the contract signing click <a class="text-blue-500" @click="reloadContractInfo" >here</a>
                </div>
            </template>
        </Modal>
        <loading-spinner v-else-if="loading" :label="loadingLabel"/>
    </div>
</template>

<script setup>

import { useCompanyStore } from "@/stores/company.js";
import {onMounted, ref} from "vue";
import { useRouter } from "vue-router";
import Modal from "@/components/Modal.vue";
import LoadingSpinner from "@/components/LoadingSpinner.vue";
import {useServicesStore} from "@/stores/services";
import { useErrorStore } from "@/stores/errors";
import { useAuthStore } from "@/stores/auth";

const auth = useAuthStore();
const company = useCompanyStore();
const errors = useErrorStore();

const router = useRouter();

const services = useServicesStore();

const loading = ref(false);

const errorMessage = ref(null);
const showContractModal = ref(true);
const completedContract = ref(false);
const loadingLabel = ref('');

onMounted(async () => {
    const queryString = window.location.search;
    const urlParams = new URLSearchParams(queryString);
    const event = urlParams.get('event')

    if(event === 'signing_complete') {
        loadingLabel.value = "Verifying contract..."
        loading.value = true
        showContractModal.value = false;
        completedContract.value = true;
        while(company.contractAccepted === false) {
            await services.apiService.getCompanyContract().then(resp => {
                company.set({contractAccepted: resp.data.data.contract_accepted});
            });
        }
        router.push('/')
        loading.value = false;
    }
    showContractModal.value = true;
})

const getContractForCompany = async () => {
    loadingLabel.value = "Loading contract..."
    loading.value = true;
    showContractModal.value = false;
    const { status, contractSignUrl, message } = await company.getNewContractForCompany();
    if (!status) {
        errorMessage.value = message;
    }
    else {
        window.open(decodeURIComponent(contractSignUrl), '_self');
    }
}

const reloadContractInfo = async () => {
    loading.value = true;
    showContractModal.value = false;
    try {
        await services.apiService.getCompanyContract().then(resp => {
            company.updateContractAccepted(resp.data.data.contract_accepted)
            if(company.contractAccepted && resp.data.data.status) {
                loading.value = false;
                router.push('/')
            } else {
                showNotSignedError()
            }

        });
    } catch {
        showNotSignedError()
    }
}

const showNotSignedError = () => {
    errors.report("Our records indicate you have not signed the contract.")
    loading.value = false;
    showContractModal.value = true;
}

</script>