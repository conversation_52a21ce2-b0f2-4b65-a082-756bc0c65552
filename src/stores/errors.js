import {ref} from 'vue';
import {defineStore} from 'pinia';

export const useErrorStore = defineStore('errors', () => {
    const errors = ref([]);

    function report(error, customOutputMessage, title) {

        if(!customOutputMessage) {
            errors.value.push(error);
        } else {
            errors.value.push({error: error, customOutputMessage: customOutputMessage, title});
        }

        // Log error to the console
        // Sentry will pick up on anything logged using console.error()
        console.error({error, customOutputMessage, title});
    }

    function $reset() {
        errors.value = [];
    }

    return {
        errors,
        report,
        $reset
    };
});