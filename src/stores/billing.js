import { defineStore } from "pinia";
import { reactive, ref, toRaw } from "vue";
import {makeApi} from "@/services/billing/factory";
import {useConfigStore} from "@/stores/config";
import {useCompanyStore} from "@/stores/company";
import {useAuthStore} from "@/stores/auth";
import {useUserStore} from "@/stores/user";

export const useBillingStore = defineStore('billing', () => {
    const company = useCompanyStore()
    const config = useConfigStore()
    const apis = makeApi(config.apiBaseUrl)
    const authStore = useAuthStore()
    const user = useUserStore()

    const getApiVersions = () => {
        const apiV3 = apis.v3;
        apiV3.setBearer(authStore.token);
        apiV3.setUserId(user.id);
        apiV3.setCompanyId(company.id);

        const apiV4 = apis.v4;
        apiV4.setBearer(authStore.token);
        apiV4.setUserId(user.id);
        apiV4.setCompanyId(company.id);
        apiV4.setIndustryKey(company.selectedIndustry);
        apiV4.setServiceKey(company.selectedService);

        return {
            v1: apiV3,
            legacy: apiV3,
            v2: apiV4,
            a20: apiV4,
        }
    }

    const companyVersionedApi = () => {
        return getApiVersions()[company.billingVersion]
    }

    const storeInitialized = ref(false);

    const billingConfigData = ref({
        // TODO: fetch from back end
        billing_status_options: [
            { name: 'Any Status', id: '' },
            { name: 'Cancelled', id: 'cancelled' },
            { name: 'Collection', id: 'collection' },
            { name: 'Initial', id: 'initial' },
            { name: 'Paid', id: 'paid' },
            { name: 'Processing', id: 'processing' },
            { name: 'Failed', id: 'failed' }
        ],
        company_summary: {},
    });

    const invoices = ref([]);

    const transactions = ref([]);

    const searchOptions = reactive({
        page: 1,
        date_range: [],
        date_start: null,
        date_end: null,
        status: null,
        invoice_id: null,
    });

    const paginationLimit = ref(0);
    const paginationTotal = ref(0);
    const paginationOffset = ref(0);

    const initialize = async () => {
        if (storeInitialized.value) return { status: true }
        const result = await search({}, true);
        await summary();
        storeInitialized.value = true;
        return result;
    }

    const summary = async () => {
        // We don't have summary call for legacy, it comes from the invoices
        if (company.billingVersion === 'v2') {
            const resp = await companyVersionedApi().getBillingSummary().catch(e=>e);
            if (resp.data?.data?.status) {
                billingConfigData.value.company_summary = resp.data.data.summary;
                return { status: true }
            }
            else {
                return genericErrorMessage(resp);
            }
        }
    }

    const search = async (page = 1, fetchConfig = false) => {
        searchOptions.page = page;
        if (searchOptions.date_range?.length === 2) {
            [ searchOptions.date_start, searchOptions.date_end ] = searchOptions.date_range;
        }
        const resp = await companyVersionedApi().searchInvoices(toRaw(searchOptions), fetchConfig).catch(e=>e);
        if (resp.data?.data?.status) {
            invoices.value = resp.data.data.invoices;
            paginationTotal.value = resp.data.data.total;
            paginationLimit.value = resp.data.data.limit;
            paginationOffset.value = resp.data.data.offset;
            if (fetchConfig) {
                Object.assign(billingConfigData.value, resp.data.data.config_data ?? {});
            }
            return { status: true }
        }
        else {
            return genericErrorMessage(resp);
        }
    }

    const downloadInvoice = async (invoiceId) => {
        const resp = await apis.v3.downloadInvoice(invoiceId).catch(e=>e);
        if (resp.data?.data?.status) {
            return { status: true, pdf: resp.data.data.pdf, filename: resp.data.data.filename }
        }
        else {
            return genericErrorMessage(resp);
        }
    }

    const payInvoiceNow = async (invoice) => {
        const invoiceVersionedApi = getApiVersions()[invoice.source]

        try {
            const resp = await invoiceVersionedApi.payInvoiceNow(invoice.id);
            if (resp.data?.data?.status) {
                return {
                    status: resp.data?.data?.status,
                    paid: resp.data?.data?.paid,
                    message: resp.data?.data?.message
                }
            }
            else {
                return { message: 'This payment has failed, please try again or contact your account manager.' }
            }
        } catch (err) {
            return { message: err?.response?.data?.message ?? 'This payment has failed, please try again or contact support' }
        }
    }

    const getTransactionsForInvoice = async (invoiceId) => {
        const resp = await apis.v3.getTransactionsForInvoice(invoiceId).catch(e=>e);
        if (resp.data?.data?.status) {
            transactions.value = resp.data.data.transactions;
            return { status: true };
        }
        else {
            return genericErrorMessage(resp);
        }
    }

    const resetSearch = () => {
        Object.assign(searchOptions, {
            page: 1,
            date_range: [],
            date_start: null,
            date_end: null,
            status: null,
            invoice_id: null,
        });
    }

    const clearStore = () => {
        invoices.value = [];
        transactions.value = [];
        storeInitialized.value = false;
    }

    const genericErrorMessage = (resp) => {
        return { status: false, message: resp.data?.data?.message || resp.err || `An unknown error occurred fetching data.` };
    }

    const $reset = () => {
        resetSearch();
        clearStore();
    }

    const deletePaymentMethod = async (id) => {
        return await companyVersionedApi().deletePaymentMethod(id);
    }

    const makePaymentMethodPrimary = async (id) => {
        return await companyVersionedApi().makePaymentMethodPrimary(id);
    }

    const getPaymentMethods = async () => {
        return await companyVersionedApi().getPaymentMethods()
    }

    const createPaymentMethod = async (token, type, data) => {
        return await companyVersionedApi().createPaymentMethod(token, type, data)
    }

    return {
        initialize,
        getTransactionsForInvoice,
        payInvoiceNow,
        downloadInvoice,
        getPaymentMethods,
        makePaymentMethodPrimary,
        deletePaymentMethod,
        createPaymentMethod,
        billingConfigData,
        invoices,
        transactions,
        searchOptions,
        paginationLimit,
        paginationTotal,
        paginationOffset,
        search,
        $reset
    }

});