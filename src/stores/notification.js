import {ref, computed} from 'vue';
import {defineStore} from 'pinia';

export const useNotificationStore = defineStore('notification', () => {
    const active = ref(false);
    const icon = ref(null);
    const title = ref(null);
    const message = ref(null);
    const callback = ref(null);
    const primaryButtonLabel = ref(null);
    const secondaryButtonLabel = ref(null);

    function set(payload) {
        icon.value = payload.icon ?? null;
        title.value = payload.title ?? null;
        message.value = payload.message ?? null;
        primaryButtonLabel.value = payload.primaryButtonLabel ?? null;
        secondaryButtonLabel.value = payload.secondaryButtonLabel ?? null;
        active.value = payload.active ?? true;
        callback.value = payload.callback ?? (() => active.value = false);
    }

    return {
        active,
        icon,
        title,
        message,
        callback,
        primaryButtonLabel,
        secondaryButtonLabel,

        set
    };
});