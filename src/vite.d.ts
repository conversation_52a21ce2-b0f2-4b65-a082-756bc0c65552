/// <reference types="vite/client" />
declare module "*.vue";

type GenericObject = {
    [key:string]: any
}

type StatusResponse = {
    status: boolean,
    message?: string,
};

type DataResponse = {
    status: boolean,
    data?: GenericObject,
    message?: string,
}

type CustomCheckboxOption = {
    id: any,
    name: string,
}

type CustomSelectOption = { label: string, value: any };

interface ImportMetaEnv {
    VITE_DEBUG: string,
    VITE_ENVIRONMENT: string,
    VITE_APP_URL: string,
    VITE_APP_NAME: string,
    VITE_API_DRIVER: string,
    VITE_API_DUMMY_DELAY: string,
    VITE_API_BASE_URL: string,
    VITE_STRIPE_PUBLISHABLE_KEY: string,
    VITE_GOOGLE_MAPS_API_KEY: string,
    VITE_FIXR_URL: string,
    VITE_DEFAULT_SUPPORT_EMAIL: string,
    VITE_DEFAULT_SUPPORT_PHONE: string,
    VITE_DEFAULT_SUPPORT_MEETING_URL: string,
    VITE_WATCHDOG_URL: string,
    VITE_WATCHDOG_PLAYBACK_URL: string,
    VITE_DROPBOX_APP_ID: string,
}