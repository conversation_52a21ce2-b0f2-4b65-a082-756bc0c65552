@tailwind base;
@tailwind components;
@tailwind utilities;

input[type="range"] {
    appearance: none;
    -webkit-appearance: none;
    border-radius: 5px;
    height: 10px;
    @apply p-0 bg-gray-300 shadow-none border-0 opacity-80;
}

input[type="range"]:hover {
    @apply opacity-100;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    margin-top: -5px;
    height: 20px;
    width: 20px;
    @apply rounded-full bg-cyan-400 border-0 cursor-pointer;
}

input[type="range"]::-moz-range-thumb {
    -webkit-appearance: none;
    appearance: none;
    height: 20px;
    width: 20px;
    @apply rounded-full bg-cyan-400 border-0 cursor-pointer;
}

input[type="range"]::-ms-thumb {
    -webkit-appearance: none;
    appearance: none;
    height: 20px;
    width: 20px;
    @apply rounded-full bg-cyan-400 border-0 cursor-pointer;
}

input[type="range"]::-moz-range-track {
    height: 10px;
    border-radius: 5px;
    @apply bg-transparent cursor-pointer;
}

input[type="range"]::-webkit-slider-runnable-track {
    height: 10px;
    border-radius: 5px;
    @apply bg-transparent cursor-pointer;
}

input[type="range"]::-ms-track {
    height: 10px;
    border-radius: 5px;
    @apply bg-transparent cursor-pointer;
}