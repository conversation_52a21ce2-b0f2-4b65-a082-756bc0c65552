<template>
    <div class="min-h-screen flex" v-if="isAuthenticated && user.MustResetPassword === false">
        <GlobalAlerts class="z-[1100]" />
        <div class="flex flex-col flex-1">
            <TopBar class="relative z-[1000]" />
            <div class="lg:flex flex-1 h-full bg-gray-100">
                <Sidebar
                    v-if="company.contractAccepted || company.bypassContractSigning"
                />
                <div class="w-full flex-auto bg-gray-100 minimum-screen-height"
                     :class="{'lg:ml-64': company.contractAccepted || company.bypassContractSigning}"
                >
                    <Banner :banner="bannerStore.banner"/>
                    <ServiceSelector></ServiceSelector>
                    <RouterView v-show="v4Checked" />
                </div>
                <ErrorModal />
            </div>
        </div>
    </div>
    <div v-else>
        <RouterView />
    </div>
</template>

<script setup>
import {onMounted, ref, watch} from "vue";
import {RouterView, useRoute} from 'vue-router'

import {useAuthStore} from "@/stores/auth";
import {useUserStore} from "@/stores/user";
import {useCompanyStore} from "@/stores/company";
import {useBannerStore} from "@/stores/banner";
import {storePersistenceService} from "@/services/stores/persist-stores";
import TopBar from "@/components/navigation/TopBar.vue";
import Sidebar from "@/components/navigation/Sidebar.vue";
import ErrorModal from "./components/errors/error-modal.vue";
import Banner from "@/components/Banner.vue";
import GlobalAlerts from "@/components/v4/GlobalAlerts.vue";
import ServiceSelector from "@/components/navigation/ServiceSelector.vue";
import {usePasswordReset} from "@/stores/passwordReset";


const auth = useAuthStore();
const user = useUserStore();
const company = useCompanyStore();
const bannerStore = useBannerStore();
const passwordReset = usePasswordReset();
const route = useRoute();
const v4Checked = ref(false);

const isAuthenticated = ref(false);

watch(() => route.meta.requiresAuth, async requiresAuth => isAuthenticated.value = requiresAuth && auth.isSessionValid());

const initializeStores = () => {
    storePersistenceService.load(auth, 'auth');
    storePersistenceService.load(user, 'user');
    storePersistenceService.load(company, 'company');
    storePersistenceService.load(passwordReset, 'password-reset');

    storePersistenceService.subscribe(auth, 'auth');
    storePersistenceService.subscribe(user, 'user');
    storePersistenceService.subscribe(company, 'company');
    storePersistenceService.subscribe(passwordReset, 'password-reset');
}

initializeStores();

onMounted(async () => {
    isAuthenticated.value = route.meta.requiresAuth && auth.isSessionValid();
    v4Checked.value = true;


    // Hotjar Tracking Code for Fixr Dashboard
    (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:6499695,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
})

</script>

<style scoped>
 .minimum-screen-height {
    min-height: calc(100vh - 80px);;
 }
</style>