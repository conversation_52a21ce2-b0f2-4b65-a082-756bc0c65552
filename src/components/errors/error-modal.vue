<template>
    <div>
        <Modal
            v-if="modalActive"
            @clicked:confirm="modalActive = false"
            :showCancelButton="false"
            confirm-label="Dismiss"
            :small="true"
        >
            <template v-slot:header>
                <div class="flex items-center">
                    <h5 class="text-md">{{latestError?.title ?? 'An error has occurred'}}</h5>
                </div>
            </template>
            <template v-slot:body>
                <p>
                    {{errorMessage}}
                </p>
            </template>
        </Modal>
    </div>
</template>

<script setup>
import { useErrorStore } from "@/stores/errors.js";
import {watch, ref, computed} from "vue";
import Modal from "@/components/Modal.vue";

const errors = useErrorStore();
const modalActive = ref(false);
const errorMessage = ref('');

const latestError = computed(() => {
    if (errors.errors.length === 0) {
        return null
    }

    return errors.errors[errors.errors.length - 1]
})

/**
 * Watch for any changes to the errorsStore 'errors' array and display the most recent one. 
 */
watch(errors.errors, () => {
    modalActive.value = true;
    errorMessage.value = latestError.value?.customOutputMessage ?? latestError.value?.message ?? latestError.value?.response ?? latestError.value;
});
</script>