<template>
    <div v-if="visible" class="flex items-center p-4 mb-4 text-sm rounded-lg" :class="styleClasses" role="alert">
        <div v-if="!noIcon" v-html="icon" class="flex-shrink-0 inline w-4 h-4 mr-3" ></div>
        <p class="flex-1" :class="[centerText ? 'text-center' : 'text-start']">{{ text }}</p>
        <button @click="handleClose" type="button" class="ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 p-1.5 inline-flex items-center justify-center h-8 w-8" data-dismiss-target="#alert-1" aria-label="Close">
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
        </button>
    </div>
</template>

<script>
// TODO - Change to heros icon ?
export const ICON_LIST = {
    INFO: `<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20"><path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/></svg>`
}
</script>

<script setup>
import {computed, onMounted, watch} from "vue";

const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },

    text: {
        type: String,
        required: true
    },

    color: {
        type: String,
        default: 'blue'
    },

    icon: {
        type: String,
        required: false
    },

    noIcon: {
        type: Boolean,
        required: false
    },

    centerText: {
        type: Boolean,
        default: false
    },

    timeout: {
        type: Number,
        required: false
    }
})

const emits = defineEmits(["update:visible"]);

const icon = computed(() => ICON_LIST[props.icon] ?? ICON_LIST.INFO)

const styleClasses = computed(() => {
    const classes = []

    // TODO - Add more styles
    switch (props.color) {
        case 'blue':
        default:
            classes.push(`bg-blue-500 text-white`)
            break;
    }

    return classes;
})

onMounted(() => {
    if (props.visible && props.timeout) createDismissTimeout()
})

watch(() => props.visible, (value) => {
    if (value && props.timeout) createDismissTimeout()
});

const handleClose = () => {
    emits('update:visible', false);
}

const createDismissTimeout = () => {
    setTimeout(handleClose, props.timeout)
}
</script>
