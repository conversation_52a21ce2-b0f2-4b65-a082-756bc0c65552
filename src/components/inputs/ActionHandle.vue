<template>
    <div class="flex items-center relative">
        <div @click="toggleActionsHandle" class="relative inline-flex items-center justify-center cursor-pointer p-2 rounded-full">
            <div class="bg-slate-800 h-1 w-1 rounded-full mr-1"></div>
            <div class="bg-slate-800  h-1 w-1 rounded-full mr-1"></div>
            <div class="bg-slate-800  h-1 w-1 rounded-full mr-1"></div>
        </div>
        <div v-if="actionsHandle" class="absolute bg-slate-50 top-0 z-50 w-40 border divide-y shadow-module rounded overflow-hidden"
             :class="[ leftSideMenu ? 'translate-x-[-10rem]' : 'left-10' ]"
        >
            <p v-if="!noEditButton" class="py-2 px-3 cursor-pointer transition duration-200"
               :class="'border-light-border bg-light-background hover:bg-light-module'"
               @click="actionClicked('edit')"
            >Edit</p>
            <p v-if="!noDeleteButton" class="py-2 px-3 border-b cursor-pointer transition duration-200"
               :class="'border-light-border bg-light-background hover:bg-light-module'"
               @click="actionClicked('delete')"
            >Delete</p>
            <p v-if="customActions?.length" class="py-2 px-3 cursor-pointer transition duration-200" v-for="customAction in customActions"
               :class="'border-light-border bg-light-module hover:bg-light-module'"
               @click="actionClicked(customAction.event)"
            >{{ customAction.name }}</p>
        </div>
        <div v-if="actionsHandle" @click="toggleActionsHandle" class="fixed z-40 w-full h-full bg-transparent">

        </div>
    </div>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
    id: {
        type: [ String, Number ],
        default: null,
    },
    noEditButton: {
        type: Boolean,
        default: false
    },
    noDeleteButton: {
        type: Boolean,
        default: false
    },
    customActions: {
        type: Array,
        default: []
    },
    leftSideMenu: {
        type: Boolean,
        default: false,
    }
});

const emit = defineEmits([ 'actionClicked' ])

const actionsHandle = ref(false);

const toggleActionsHandle = () => {
    actionsHandle.value = !actionsHandle.value;
}

const actionClicked = (action) => {
    toggleActionsHandle();
    emit('actionClicked', action, props.id);
}

</script>
