<template>
    <div class="w-full relative" v-click-outside="hide">
        <span v-if="label" class="block mb-1 text-sm font-medium">{{ label }}</span>
        <div
            class="relative transition-colors w-full cursor-pointer border border-gray-200 px-4 py-2 relative flex hover:border-cyan-500 hover:text-cyan-500"
            :class="{'border-cyan-600 text-cyan-600 rounded-t-lg': active, 'rounded-lg': !active}"
            @click="toggle"
        >
            <p v-if="!modelValue" class="text-gray-500">{{ placeholder }}</p>
            <p v-if="modelValue" class="text-gray-900 capitalize">{{ modelValue }}</p>

            <div class="absolute right-0 top-0 bottom-0 flex items-center mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="currentColor" class="w-4 h-4">
                    <path stroke-linecap="round" stroke-linejoin="round"
                          class="transition-all"
                          :d="active ? 'M4.5 15.75l7.5-7.5 7.5 7.5' : 'M19.5 8.25l-7.5 7.5-7.5-7.5'"
                    />
                </svg>
            </div>
        </div>

        <div
            v-if="active"
            class="absolute w-full rounded-b-lg border-l border-r border-b border-gray-200 max-h-64 overflow-y-scroll bg-white z-10 text-black"
            :class="{'border-cyan-500': active}"
        >
            <div
                v-for="(option, idx) in options"
                @click="select(option)"
                class="px-4 py-2 hover:bg-blue-50 cursor-pointer capitalize"
            >
                {{ option }}
            </div>
        </div>
    </div>
</template>

<script setup>
import {ref} from "vue";

const props = defineProps({
    modelValue: String,
    placeholder: {
        type: String,
        default: "Select a value"
    },
    options: {
        type: Array,
        default: []
    },
    label: {
        type: String,
        default: '',
    }
});

const active = ref(false);
const emit = defineEmits(['update:modelValue']);

const toggle = () => active.value = !active.value;
const hide = () => active.value = false;

const select = val => {
    emit('update:modelValue', val);
    hide();
}
</script>