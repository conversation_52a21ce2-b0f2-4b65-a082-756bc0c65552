<template>
    <div class="relative">
        <label v-if="label" class="block mb-1 text-sm font-medium">
            {{ label }}
        </label>
        <input v-if="inputType === 'text'" class="w-full block placeholder:font-light font-normal placeholder:text-slate-400 focus:outline-none focus:border-cyan-500 bg-white border border-slate-300 rounded-md py-2 px-4 h-11"
               @blur="closeMenu()"
               @input="onInput()"
               @keydown="onKeyDown"
               @keyup="onKeyUp"
               :placeholder="placeholder"
               v-model="currentInput"
               type="text"
               ref="input"
        />
        <textarea
            v-else
            class="w-full border rounded pl-4 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
            @blur="closeMenu()"
            @input="onInput()"
            @keydown="onKeyDown"
            @keyup="onKeyUp"
            rows="3"
            :placeholder="placeholder"
            v-model="currentInput"
            ref="input"
        />
        <div v-if="maxCharacterCount">{{modelValue.length}} / {{maxCharacterCount}}</div>

        <div class="absolute z-50 -top-1.5" v-if="showMenu">
            <div
                class="overflow-auto rounded-lg shadow-md bg-slate-50 py-2 border border-slate-400 text-xs absolute bottom-full">
                <ul class="list-reset">
                    <li v-if="displayedItems.length === 0">
                        <span class="block px-4 py-1 text-gray-500 text-nowrap whitespace-nowrap">No results</span>
                    </li>
                    <li v-else v-for="(code, index) in displayedItems"
                        class="px-4 py-1 flex no-underline hover:no-underline transition-colors duration-100 text-nowrap whitespace-nowrap"
                        :class="selectedIndex == index ? 'bg-cyan-500 text-white' : 'hover:bg-gray-100'"
                        @click.prevent="applyShortcode(index)"
                    >
                        <span class="font-bold">{{ code.label }}</span>
                        <span class="ml-2">{{ openTag + code.value + closeTag }}</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, ComputedRef, nextTick, ref, Ref, useTemplateRef } from "vue";

type Shortcode = {
    value: string,
    label: string,
};

interface Props {
    openTag?: string,
    closeTag?: string,
    placeholder?: string,
    itemLimit?: number,
    modelValue: string,
    allShortcodes: Shortcode[],
    label?: string,
    inputType?: string,
    maxCharacterCount?: number,
}
const props = withDefaults(defineProps<Props>(), {
    openTag: '[',
    closeTag: ']',
    placeholder: '',
    modelValue: '',
    inputType: 'text',
    allShortcodes: () => [],
    itemLimit: 5,
});

const emit = defineEmits(['update:modelValue']);

const inputElement = useTemplateRef('input');
const currentInput: Ref<string> = ref(props.modelValue);

const selectedIndex: Ref<number> = ref(0);
const showMenu: Ref<boolean> = ref(false);
const lastSearchText: Ref<string|null> = ref(null);
const currentSearchText: Ref<string|null> = ref(null);
const currentKey: Ref<string|null> = ref(null);
const currentKeyIndex: Ref<number|null> = ref(null);
const cancelKeyUp: Ref<string|null> = ref(null);

const displayedItems: ComputedRef<Shortcode[]> = computed(() => filteredItems.value.slice(0, props.itemLimit));
const filteredItems: ComputedRef<Shortcode[]> = computed(() => {
    if (!currentSearchText.value)
        return props.allShortcodes;
    
    return props.allShortcodes.filter(item => item.value.toLowerCase().includes(currentSearchText.value!.toLowerCase()));
});

const onInput = () => {
    checkKey();
    updateModelValue();
}

const checkKey = () => {
    const index = getSelectionStart();

    if (index >= 0) {
        const {key, keyIndex} = getLastKeyBeforeCaret(index);
        const searchText = lastSearchText.value = getLastSearchText(index, keyIndex);
        if (!(keyIndex < 1 || /[\s\/-]/.test(currentInput.value[keyIndex - 1])))
            return false;

        if (searchText != null) {
            openMenu(key, keyIndex);
            currentSearchText.value = searchText;
            return true;
        }
    }
}

const onKeyDown = (ev: KeyboardEvent) => {
    if (currentKey.value) {
        if ([props.openTag].includes(ev.key))
            cancelEvent(ev);
        else if (ev.key === 'ArrowDown') {
            selectedIndex.value++;
            if (selectedIndex.value >= displayedItems.value.length)
                selectedIndex.value = 0;

            cancelEvent(ev);
        } else if (ev.key === 'ArrowUp') {
            selectedIndex.value--;
            if (selectedIndex.value < 0)
                selectedIndex.value = displayedItems.value.length - 1;

            cancelEvent(ev);
        } else if (ev.key === 'Enter' || ev.key === 'Tab' && displayedItems.value.length > 0) {
            applyShortcode(selectedIndex.value);
            cancelEvent(ev);
        } else if (['Escape', props.closeTag].includes(ev.key)) {
            if (ev.key === props.closeTag)
                currentInput.value += props.closeTag;
            closeMenu();
            cancelEvent(ev);
        } else {
            selectedIndex.value = 0;
        }
    }
}

const onKeyUp = (ev: KeyboardEvent) => {
    if (cancelKeyUp.value && ev.key === cancelKeyUp.value)
        cancelEvent(ev);

    cancelKeyUp.value = null;
}

const cancelEvent = (ev: KeyboardEvent) => {
    ev.preventDefault();
    ev.stopPropagation();
    cancelKeyUp.value = ev.key;
}

const applyShortcode = async (index: number) => {
    const item = displayedItems.value[index];
    if (!item)
        return;

    const value = (currentKey.value || '') + String(item.value) + props.closeTag;
    currentInput.value = replaceText(currentInput.value, currentSearchText.value ?? '', value, currentKeyIndex.value ?? 0);
    updateModelValue();
    setCaretPosition((currentKeyIndex.value ?? 0) + value.length);
    closeMenu();
}

const setCaretPosition = async (index: number) => {
    await nextTick(() => inputElement.value!.selectionEnd = index);
};

const replaceText = (text: string, searchText: string, newText: string, index: number) => {
    return text.slice(0, index) + newText + text.slice(index + searchText.length + 1, text.length);
}

const openMenu = (key: string, keyIndex: number): void => {
    if (currentKey.value !== key && !showMenu.value) {
        currentKey.value = key;
        currentKeyIndex.value = keyIndex;
        selectedIndex.value = 0;
        showMenu.value = true;
    }
}

const closeMenu = () => {
    if (currentKey.value !== null) {
        currentKey.value = null;
        showMenu.value = false;
    }
}

const getLastSearchText = (caretIndex: number, keyIndex: number): string|null => {
    if (keyIndex !== -1) {
        const searchText = currentInput.value.substring(keyIndex + 1, caretIndex);
        if (!/[\s\/-]/.test(searchText))
            return searchText;
    }

    return null;
}

const getLastKeyBeforeCaret = (caretIndex: number) => {
    return {
        key: props.openTag,
        keyIndex: currentInput.value.lastIndexOf(props.openTag, caretIndex - 1),
    }
}

const getSelectionStart = () => {
     return inputElement.value?.selectionStart ?? 0;
}

const updateModelValue = () => {
    emit('update:modelValue', currentInput.value);
}

</script>

