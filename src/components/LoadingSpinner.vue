<template>
    <div class="h-full z-50 bg-opacity-0 bg-slate-600 inset-0 flex items-center backdrop-blur-sm justify-center"
        :class="[inline ? 'block' : fullscreen ? 'fixed' : 'absolute']"
    >
        <div>
            <div class="border-cyan-500 loading-spinner rounded-full mx-auto relative"
                :class="[tiny ? 'w-8 h-8 border-r-2'
                : small ? 'w-24 h-24 border-r-2'
                : 'w-32 h-32 border-r-4']"
            />
            <div v-if="label" class="pt-4">
                {{ label }}
            </div>
        </div>
    </div>
</template>

<script setup>

const props = defineProps({
    small: {
        type: Boolean,
        default: false
    },
    fullscreen: {
        type: Boolean,
        default: false,
    },
    inline: {
        type: Boolean,
        default: false,
    },
    tiny: {
        type: Boolean,
        default: false,
    },
    label: {
        type: String,
        default: ''
    }
});

</script>

<style scoped>
.loading-spinner {
    animation: spin 1.3s linear infinite;
}

@keyframes spin {
    0%   { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>