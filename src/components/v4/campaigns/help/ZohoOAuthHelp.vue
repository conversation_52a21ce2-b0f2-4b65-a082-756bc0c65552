<template>
    <div class="w-full whitespace-pre-line p-3">
        <p>
            <strong>Zoho Authentication process:</strong>
            <br>Setting up Zoho OAuth requires access to the Zoho API Console in order to generate the initial Grant Token. Without this token, authentication with ZohoCRM is not possible.
            <br>
            <br>- The tokens/keys in the screenshots below are cut off, and don’t show the correct key length
            <br>- At this time, the US domains for Zoho are hard coded into the delivery service, so international Zoho domains will not function.
            <br>
            <br class="font-semibold">1. Someone from the company will need to access their Zoho API settings at the
            <a href="https://api-console.zoho.com" target="_blank" class="text-cyan-500">
                Zoho API Console
            </a>
            and generate a token:
        </p>
        <img class="my-3"
             :src="images[0].url"
             :alt="images[0].id"
        />
        <div>
            <p class="font-semibold py-2"><span class="underline">Important:</span> the Scope field must include either <span class="text-cyan-500">ZohoCRM.modules.leads.CREATE</span> or <span class="text-cyan-500">ZohoCRM.modules.leads.ALL</span></p>
            <p>Once the Grant Token has been generated, there is a limited time to register it (max 10 minutes). Once it’s successfully registered, a Refresh Token will be received which will last until revoked.</p>
            <br>
            <br>
            <p class="font-semibold">2. The resulting Grant Token will be displayed. The other two required fields are in the Client Secret tab in Zoho API console.</p>
        </div>
        <img class="my-3"
             :src="images[1].url"
             :alt="images[1].id"
        />
        <img class="my-3"
             :src="images[2].url"
             :alt="images[2].id"
        />
        <p class="mt-3">
            These need to be added to the campaign's Zoho CRM Integration - System Fields. The generated code is the Grant Token:
        </p>
        <img class="my-3"
             :src="images[3].url"
             :alt="images[3].id"
        />
        <p class="font-semibold">3. The token can now be registered with the blue Register button.</p>
        <p>If the fields are correct, and the scope was set correctly, the text next to the button should indicate that the Grant Token was accepted. At this point, it is best to save the Integration by hitting Create/Update, then saving the whole Campaign. This will ensure the Refresh Token is saved to the database.</p>

        <p class="font-semibold mt-2">4. Re-opening the CRM Integration, the text next to the Register button should indicate that this integration is registered (has a saved Refresh Token).</p>
        <p>This token is not displayed on the front end. The Register button should not be re-used unless a new Grant Token has been generated and needs to be registered.</p>
    </div>
</template>

<script setup lang="ts">
const images: { url: string, id: string }[] = [
    {
        id: 'zoho_1.png',
        url: 'https://storage.googleapis.com/a2_production_assets/company_logos/90B246A338/media_assets/bee40e6c13dd733b60b558286f2bf144a2028d981c7a66ee6f5fedd3f6250847.png',
    },
    {
        id: 'zoho_2.png',
        url: 'https://storage.googleapis.com/a2_production_assets/company_logos/90B246A338/media_assets/b1ac7586dc66069e803af49767120cd451af9183f4b8c29470d89b3cfa22a326.png',
    },
    {
        id: 'zoho_3.png',
        url: 'https://storage.googleapis.com/a2_production_assets/company_logos/90B246A338/media_assets/b7aaa0d1c0c065ca2dac0bd9bf2744c69e5798affac3b02a0025a2115ff610d8.png',
    },
    {
        id: 'zoho_4.png',
        url: 'https://storage.googleapis.com/a2_production_assets/company_logos/90B246A338/media_assets/f84125e856ea32f0ca5cc6fa50941f91f6e1bd5eea281686398d61f2c4e89244.png',
    },
];
</script>