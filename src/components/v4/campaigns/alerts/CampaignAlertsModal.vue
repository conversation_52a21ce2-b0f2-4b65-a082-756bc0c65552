<template>
    <Modal @clicked:cancel="emit('close')" :show-confirm-button="false" :cancel-label="'Close'">
        <template v-slot:header>
            Campaign Alerts
        </template>
        <template v-slot:body>
            <div v-if="showForm">
                <div class="grid md:grid-cols-3 mx-5 md:mx-[10rem] gap-5 items-center my-5 md:my-[5rem]">
                    <p class="md:text-right">Usage Threshold:</p>
                    <div class="md:col-span-2">
                        <CustomInput v-model="editingAlert.payload.campaign_usage_threshold" prefix-text="$" :input-disabled="saving"/>
                    </div>
                    <p class="md:text-right">Alert Recipients:</p>
                    <div class="md:col-span-2">
                        <CompanyContactMultiselect :disabled="saving" :contacts="groupedRecipients(editingAlert.recipients)" @contact-selected="processSelectedContacts"/>
                    </div>
                </div>
                <div class="flex justify-end gap-5 mr-5 mb-5 md:mb-auto">
                    <SolidButton :classes="'px-7'" :disabled="saving" @click="save">Save</SolidButton>
                    <OutlineButton @click="hideForm" :disabled="saving">Cancel</OutlineButton>
                </div>                
            </div>
            <div v-else>
                <div class="flex justify-end mx-3 my-3 md:my-auto">
                    <SolidButton @click="showCreateForm" :disabled="saving">Create New Alert</SolidButton>
                </div>
                <div class="rounded-md bg-blue-50 p-4 my-5 mx-3">
                    <div class="flex items-start">
                        <div class="shrink-0">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="h-5 w-5 text-blue-400" aria-hidden="true">
                                <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm8.706-1.442c1.146-.573 2.437.463 2.126 1.706l-.709 2.836.042-.02a.75.75 0 0 1 .67 1.34l-.04.022c-1.147.573-2.438-.463-2.127-1.706l.71-2.836-.042.02a.75.75 0 1 1-.671-1.34l.041-.022ZM12 9a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z" clip-rule="evenodd" />
                            </svg>

                        </div>
                        <div class="ml-3 flex-1 md:flex md:justify-between">
                            <p class="text-sm text-blue-700">
                                <span class="font-semibold">Note:</span>
                                Alerts are triggered each time campaign usage exceeds the defined threshold. For example, if set at $1,000, alerts will be triggered at $1,000,
                                $2,000, $3,000, and so on. Usage totals reset on the first of each month.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="md:grid grid-cols-4 gap-5 mt-9 mx-3 text-xs text-gray-700 uppercase bg-cyan-50 font-bold border-b border-gray-200 px-4 py-3 hidden">
                    <p>Usage Threshold</p>
                    <p>Recipients</p>
                    <p class="text-center">Active</p>
                    <p class="text-center">Actions</p>
                </div>
                <div v-for="alert in alerts" class="grid md:grid-cols-4 grid-cols-3 gap-5 mx-3 text-sm text-gray-900 odd:bg-gray-50 border-b border-gray-200 px-4 py-6 items-center">
                    <p class="font-bold text-sm md:hidden text-gray-900">Usage Threshold</p>
                    <div class="col-span-2 md:col-span-1">
                        <p>${{ Number(alert.payload['campaign_usage_threshold']).toLocaleString() }}</p>
                    </div>
                    <p class="font-bold text-sm md:hidden text-gray-900">Recipients</p>
                    <div class="grid col-span-2 md:col-span-1">
                        <div v-for="recipient in groupedRecipients(alert.recipients)" class="flex gap-2 my-1 items-center">
                            <span class="col-span-3">{{ recipient.name }}</span>
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5 text-blue-500"
                                 v-if="recipient.emailActive">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                      d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"/>
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5 text-blue-500"
                                 v-if="recipient.smsActive">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                      d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.************.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"/>
                            </svg>
                        </div>
                    </div>
                    <p class="font-bold text-sm md:hidden text-gray-900">Active</p>
                    <div class="md:text-center col-span-2 md:col-span-1">
                        <ToggleSwitch v-model="alert.active" small @update:modelValue="toggleActive(alert)"></ToggleSwitch>
                    </div>
                    <p class="font-bold text-sm md:hidden text-gray-900">Actions</p>
                    <p class="flex gap-4 md:justify-center items-center col-span-2 md:col-span-1" :class="[{'opacity-50 pointer-events-none': saving}]">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                             class="h-6 w-6 text-blue-500 cursor-pointer" @click="edit(alert)">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                  d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"/>
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                             class="h-6 w-6 text-red-500 cursor-pointer" @click="deleteAlert(alert)">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                  d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"/>
                        </svg>
                    </p>
                </div>
            </div>
        </template>
    </Modal>
</template>

<script setup lang="ts">

import Modal from "@/components/Modal.vue";
import {CampaignAlert, CampaignAlertRecipient, useFutureCampaignStore} from "@/stores/v4/future-campaigns";
import {onBeforeMount, Ref, ref} from "vue";
import SolidButton from "@/components/inputs/SolidButton.vue";
import ToggleSwitch from "@/components/inputs/ToggleSwitch.vue";
import OutlineButton from "@/components/inputs/OutlineButton.vue";
import CustomInput from "@/components/inputs/CustomInput.vue";
import {useCompanyUsersStore} from "@/stores/company-users";
import CompanyContactMultiselect from "@/components/v4/campaigns/alerts/CompanyContactMultiselect.vue";
import {useErrorStore} from "@/stores/errors";

type AlertRecipient = {
    id: number,
    name: string,
    emailActive: boolean,
    smsActive: boolean
}

const emit = defineEmits(['close']);

const props = defineProps({
    campaignReference: {
        type: String,
        required: true
    }
});

const campaignStore = useFutureCampaignStore();
const alerts: Ref<CampaignAlert[]> = ref([]);
const campaignAlerts: Ref<CampaignAlert[]> = ref([]);
const showForm: Ref<boolean> = ref(false);
const editingAlert: Ref<CampaignAlert|GenericObject> = ref({});
const userStore = useCompanyUsersStore();
const errorStore = useErrorStore();
const saving = ref(false);

onBeforeMount(() => {
    campaignAlerts.value = campaignStore.scopedCampaignList.find(campaign => campaign.reference === props.campaignReference)?.campaign_alerts ?? [];
    initializeAlerts();
    userStore.initialize();
    resetEditing();
})

const initializeAlerts = () => {
    alerts.value = JSON.parse(JSON.stringify(campaignAlerts.value));
}

const hideForm = () => {
    resetEditing();
    showForm.value = false;
}

const showCreateForm = () => {
    resetEditing();
    showForm.value = true;
} 

const edit = (alert: CampaignAlert) => {
    editingAlert.value = JSON.parse(JSON.stringify(alert));
    showForm.value = true;
}

const resetEditing = () => {
    editingAlert.value = {
        payload: {},
        recipients: [],
        type: 'campaign_threshold',
        active: true
    };
}  

const save = () => {
    if (!validate()) {
        return;
    }
    
    saving.value = true;
    campaignStore.saveCampaignAlert(editingAlert.value, props.campaignReference, editingAlert.value.id)
        .then(resp => {
            showForm.value = false;
            
            if (!editingAlert.value.id) {
                campaignAlerts.value.push(resp.data.data.alert);
                return;
            } 
            
            const index = campaignAlerts.value.findIndex(alert => alert.id === editingAlert.value.id);
            
            if (index !== -1) {
                campaignAlerts.value[index] = resp.data.data.alert;
            }
        })
        .catch(e => reportError(e))
        .finally(() => { 
            initializeAlerts();
            saving.value = false;
        });
}

const deleteAlert = (alert: CampaignAlert) => {
    saving.value = true;
    campaignStore.deleteCampaignAlert(alert.id, props.campaignReference)
        .then(() => {
            const index = alerts.value.findIndex(a => a.id === alert.id);
            
            if (index !== -1) {
                alerts.value.splice(index, 1);
                campaignAlerts.value.splice(index, 1);
            }
        })
        .catch(e => reportError(e))
        .finally(() => saving.value = false);
}

const toggleActive = (alert: CampaignAlert) => {
    saving.value = true;
    campaignStore.toggleCampaignAlertActive(alert.id, props.campaignReference)
        .then(resp => {
            alert.active = resp.data.data.active;
            const campaignAlert = campaignAlerts.value.find(a => a.id === alert.id);
            
            if (campaignAlert) {
                campaignAlert.active = alert.active;
            }
        })
        .catch(e => reportError(e))
        .finally(() => saving.value = false);
}

const validate = () => {
    if (!editingAlert.value.payload.campaign_usage_threshold) {
        errorStore.report('Campaign usage threshold is required.');
        return false;
    }
    
    if (!editingAlert.value.recipients.length) {
        errorStore.report('Please select at least one alert recipient.');
        return false;
    }
    
    return true;
}

const reportError = (e: any) => {
    console.error(e);
    errorStore.report('Something went wrong. Please try again or contact support');
} 

const groupedRecipients = (recipients: CampaignAlertRecipient[]) => {
    const groupedRecipients: AlertRecipient[] = [];
    
    recipients.forEach(recipient => {
        const group = groupedRecipients.find(g => g.id === recipient.id);
        
        if (group) {
            switch (recipient.channel) {
                case 'sms':
                    group.smsActive = true;
                    break;
                case 'email':
                default:
                    group.emailActive = true;
                    break;
            }
        } else {
            groupedRecipients.push({
                id: recipient.id,
                name: recipient.name,
                emailActive: recipient.channel === 'email',
                smsActive: recipient.channel === 'sms'
            });
        }
    });
    
    return groupedRecipients;
}

const processSelectedContacts = (contacts: AlertRecipient[]) => {
    editingAlert.value.recipients = [];
    contacts.forEach(contact => {
        if (contact.emailActive) {
            editingAlert.value.recipients.push({
                id: contact.id,
                phone: undefined,
                email: undefined,
                name: contact.name,
                channel: 'email'
            })
        }

        if (contact.smsActive) {
            editingAlert.value.recipients.push({
                id: contact.id,
                phone: undefined,
                email: undefined,
                name: contact.name,
                channel: 'sms'
            })
        }
    });
}

</script>