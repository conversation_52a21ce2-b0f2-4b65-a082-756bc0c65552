<template>
    <div class="relative min-w-[20rem]" v-click-outside="() => isOpen = false" :class="[{'opacity-50 pointer-events-none': disabled}]">
        <div @click="isOpen = !isOpen" class="w-full p-2 border rounded-md bg-white shadow-md cursor-pointer flex flex-wrap gap-2 min-h-[2.5rem] items-center">            
            <p class="text-gray-500 mx-auto" v-if="selectedUsers.length === 0">{{ placeHolder }}</p>
            <div v-else v-for="user in selectedUsers" :key="user.id" class="flex items-center bg-blue-100 text-blue-600 px-2 py-1 rounded-md text-sm">
                {{ user.name }}
                <svg @click.stop="toggleUserSelection(user)" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="h-4 w-4 ml-1 cursor-pointer hover:text-red-500">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </div>
        </div>
        <div v-if="isOpen" class="absolute mt-2 w-full bg-white border rounded-md shadow-md">
            <ul class="max-h-60 overflow-auto">
                <li
                    v-for="user in users"
                    :key="user.id"
                    class="p-2 flex justify-between items-center hover:bg-gray-100 gap-3 text-gray-700"
                   >
                    <p class="flex-1" :class="{'font-semibold text-blue-500': selected(user)}">{{ user.name }}</p>
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5 cursor-pointer"
                         :class="{'text-blue-500 h-6 w-6': user.emailActive}" @click="user.emailActive = !user.emailActive">
                        <path stroke-linecap="round" stroke-linejoin="round"
                              d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"/>
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5 cursor-pointer"
                         :class="{'text-blue-500 h-6 w-6': user.smsActive}" @click="user.smsActive = !user.smsActive">
                        <path stroke-linecap="round" stroke-linejoin="round"
                              d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.************.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"/>
                    </svg>
                </li>
            </ul>
        </div>
    </div>
</template>

<script setup>
import {computed, onBeforeMount, ref, watch} from 'vue';
import {useCompanyUsersStore} from "@/stores/company-users";

const emit = defineEmits(['contact-selected']);

const isOpen = ref(false);
const userStore = useCompanyUsersStore();
const users = ref([]);
const props = defineProps({
    placeHolder: {
        type: String,
        default: 'Select'
    },
    disabled: {
        type: Boolean,
        default: false
    },
    contacts: {
        type: Array,
        default: []
    }
});

onBeforeMount(async () => {
    await userStore.initialize();
    users.value = userStore.companyUsers.map(user => {
        const contact = props.contacts.find(c => c.id === user.id);
        
        return {
            id: user.id,
            name: user.name,
            emailActive: contact?.emailActive ?? false,
            smsActive: contact?.smsActive ?? false
        }
    })
})

const selected = (user) => user.emailActive || user.smsActive;
const toggleUserSelection = (user) => {
    user.emailActive = false;
    user.smsActive = false;
};
const selectedUsers = computed(() => users.value.filter(user => selected(user)));
const activeCount = computed(() => {
    let count = 0;
    
    users.value.forEach(user => {
        count += user.emailActive ? 1 : 0;
        count += user.smsActive ? 1 : 0;
    })
    
    return count;
});

watch(activeCount, () => {
    emit('contact-selected', selectedUsers.value);
});

</script>
