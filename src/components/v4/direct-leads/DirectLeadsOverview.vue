<template>
    <ProductSearch :stores-initialized="storesInitialized" :title="'Direct Leads'" />
</template>

<script setup lang="ts">
import { onBeforeMount, ref } from "vue";
import { useFutureCampaignStore } from "@/stores/v4/future-campaigns";
import { useProductAssignmentStore } from "@/stores/v4/products";
import { ProductType, useServicesStore } from "@/stores/services";
import ProductSearch from "@/components/v4/products/ProductSearch.vue";

const campaignStore = useFutureCampaignStore();
const productStore = useProductAssignmentStore();
const services = useServicesStore();
const storesInitialized = ref(false);

onBeforeMount(async () => {
    const productScope = productStore.productScope;

    services.apiServiceV4.setProductKey(ProductType.DirectLeads);
    await Promise.all([
        campaignStore.initialize(productScope !== ProductType.DirectLeads),
        productStore.initialize(),
    ]);
    storesInitialized.value = true;
});

</script>