<template>
    <VueEcharts :option="chartOptions" :theme="{color: chartColors}" :style="`height: ${height}; width: ${width}`" />
</template>

<script setup>
import { VueEcharts } from 'vue3-echarts';

const props = defineProps({
    chartOptions: {
        type: Object,
        default: () => ({})
    },
    chartColors: {
        type: Array,
        default: ['#2563eb', '#00A3FF']
    },
    height: {
        type: String,
        default: '300px'
    },
    width: {
        type: String,
        default: '100%'
    }
});
</script>