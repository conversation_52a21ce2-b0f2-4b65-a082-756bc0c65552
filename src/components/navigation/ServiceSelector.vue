<template>
    <div v-if="route.name.startsWith('leads') || route.name.startsWith('dashboard')" class="bg-gray-100 w-full relative"
         :class="[company.services[company.selectedIndustry]?.length ?? 0 > 1 ? 'h-[5.75rem]' : 'h-[3.5rem]']">
        <div class="bg-white fixed top-16 md:top-20 z-[900] border-b border-gray-200 shadow-sm right-0 w-full lg:w-[calc(100%-16rem)]">
            <div class="overflow-x-auto flex items-center px-4 py-2 gap-2">
                <div v-for="(industry, idx) in company.services">
                    <p @click="select(idx)"
                       class="rounded text-sm md:text-base px-5 py-2 font-bold text-black capitalize tracking-tight cursor-pointer whitespace-nowrap"
                       :class="[company.selectedIndustry === idx ? 'shadow-md shadow-cyan-100 bg-gradient-to-tl from-cyan-400 to-cyan-100' : 'font-semibold bg-white hover:bg-cyan-50']">
                        {{ useProperCase(idx) }}
                    </p>
                </div>
            </div>
            <div
                v-if="company.services[company.selectedIndustry]?.length ?? 0 > 1"
                class="overflow-x-auto flex items-center pb-2 px-4 gap-2">
                <div v-for="service in company.services[company.selectedIndustry]"
                     @click="select(service)"
                     class="px-5 py-1 rounded text-xs md:text-sm font-bold tracking-tight text-black capitalize cursor-pointer whitespace-nowrap"
                     :class="[company.selectedService === service.slug ? 'shadow-md shadow-cyan-100 bg-gradient-to-tl from-sky-200 to-sky-100' : 'bg-white hover:bg-cyan-50']">
                    {{ service.name }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useCompanyStore } from "@/stores/company";
import { useProperCase } from "@/composables/useProperCase.js";
import { useRoute } from "vue-router";

const route = useRoute();
const company = useCompanyStore();

const select = async val => {
    if(company.services[val]) {
        await company.$patch({selectedIndustry: val, selectedService: company.services[val][0].slug});
        if (company.selectedService) {
            location.reload();
        }
    }
    else {
        await company.$patch({selectedIndustry: val.industry_slug, selectedService: val.slug});
        if (company.selectedService) {
            location.reload();
        }
    }
}

</script>