apiVersion: apps/v1
kind: Deployment
metadata:
  name: fixr-example-dashboard-deployment
spec:
  replicas: 2
  selector:
    matchLabels:
      app: fixr-example-dashboard-deployment
  template:
    metadata:
      labels:
        app: fixr-example-dashboard-deployment
    spec:
      containers:
        - name: server
          image: gcr.io/example-project/example-container:example-version
          ports:
            - containerPort: 80
          resources:
            requests:
              memory: "2048Mi"
              cpu: "500m"
            limits:
              memory: "8192Mi"
              cpu: "2000m"