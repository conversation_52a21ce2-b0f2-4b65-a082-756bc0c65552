apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fixr-example-dashboard-ingress
  annotations:
    kubernetes.io/ingress.global-static-ip-name: "fixr-example-dashboard-static-ip"
    networking.gke.io/v1beta1.FrontendConfig: "fixr-example-dashboard-ingress-frontend-config"
    networking.gke.io/managed-certificates: "fixr-example-dashboard-ssl-certificate"
spec:
  defaultBackend:
    service:
      name: fixr-example-dashboard-node-port
      port:
        number: 80
  rules:
    - host: example-domain.fixr.com
      http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: fixr-example-dashboard-node-port
                port:
                  number: 80
