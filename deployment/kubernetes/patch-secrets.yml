spec:
  template:
    spec:
      containers:
        - name: server
          env:
            - name: VITE_DEBUG
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: VITE_DEBUG
            - name: VITE_ENVIRONMENT
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: VITE_ENVIRONMENT
            - name: VITE_APP_URL
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: VITE_APP_URL
            - name: VITE_API_DRIVER
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: VITE_API_DRIVER
            - name: VITE_API_DUMMY_DELAY
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: VITE_API_DUMMY_DELAY
            - name: VITE_API_BASE_URL
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: VITE_API_BASE_URL
            - name: VITE_API_PROXY_TARGET
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: VITE_API_PROXY_TARGET
            - name: VITE_STRIPE_PUBLISHABLE_KEY
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: VITE_STRIPE_PUBLISHABLE_KEY
            - name: VITE_GOOGLE_MAPS_API_KEY
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: VITE_GOOGLE_MAPS_API_KEY
            - name: VITE_FIXR_URL
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: VITE_FIXR_URL
            - name: VITE_DEFAULT_SUPPORT_EMAIL
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: VITE_DEFAULT_SUPPORT_EMAIL
            - name: VITE_DEFAULT_SUPPORT_PHONE
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: VITE_DEFAULT_SUPPORT_PHONE
            - name: VITE_DEFAULT_SUPPORT_MEETING_URL
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: VITE_DEFAULT_SUPPORT_MEETING_URL
            - name: VITE_WATCHDOG_URL
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: VITE_WATCHDOG_URL
            - name: VITE_WATCHDOG_PLAYBACK_URL
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: VITE_WATCHDOG_PLAYBACK_URL
            - name: VITE_DROPBOX_APP_ID
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: VITE_DROPBOX_APP_ID
            - name: IMAGE_NAME
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: IMAGE_NAME
            - name: DEPLOYMENT_NAME
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: DEPLOYMENT_NAME
            - name: PROJECT_ID
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: PROJECT_ID
            - name: CLUSTER_NAME
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: CLUSTER_NAME
            - name: DEFAULT_ZONE
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: DEFAULT_ZONE
            - name: KEY_FILE
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: KEY_FILE
            - name: DOCKER_NAME
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: DOCKER_NAME
            - name: KUBERNETES_DEPLOYMENT_NAME
              valueFrom:
                secretKeyRef:
                  name: fixr-example-dashboard-secret
                  key: KUBERNETES_DEPLOYMENT_NAME