#!/bin/bash
echo VITE_DEBUG=${VITE_DEBUG} | tee -a .env
echo VITE_ENVIRONMENT=${VITE_ENVIRONMENT} | tee -a .env
echo VITE_APP_URL=${VITE_APP_URL} | tee -a .env
echo VITE_API_DRIVER=${VITE_API_DRIVER} | tee -a .env
echo VITE_API_DUMMY_DELAY=${VITE_API_DUMMY_DELAY} | tee -a .env
echo VITE_API_BASE_URL=${VITE_API_BASE_URL} | tee -a .env
echo VITE_API_PROXY_TARGET=${VITE_API_PROXY_TARGET} | tee -a .env
echo VITE_STRIPE_PUBLISHABLE_KEY=${VITE_STRIPE_PUBLISHABLE_KEY} | tee -a .env
echo VITE_GOOGLE_MAPS_API_KEY=${VITE_GOOGLE_MAPS_API_KEY} | tee -a .env
echo VITE_FIXR_URL=${VITE_FIXR_URL} | tee -a .env
echo VITE_DEFAULT_SUPPORT_EMAIL=${VITE_DEFAULT_SUPPORT_EMAIL} | tee -a .env
echo VITE_DEFAULT_SUPPORT_PHONE=${VITE_DEFAULT_SUPPORT_PHONE} | tee -a .env
echo VITE_DEFAULT_SUPPORT_MEETING_URL=${VITE_DEFAULT_SUPPORT_MEETING_URL} | tee -a .env
echo VITE_WATCHDOG_URL=${VITE_WATCHDOG_URL} | tee -a .env
echo VITE_WATCHDOG_PLAYBACK_URL=${VITE_WATCHDOG_PLAYBACK_URL} | tee -a .env
echo VITE_DROPBOX_APP_ID=${VITE_DROPBOX_APP_ID} | tee -a .env