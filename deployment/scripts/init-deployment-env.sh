#!/bin/bash
echo export VITE_DEBUG=${VITE_DEBUG} | tee -a .deployment-env
echo export VITE_ENVIRONMENT=${VITE_ENVIRONMENT} | tee -a .deployment-env
echo export VITE_APP_URL=${VITE_APP_URL} | tee -a .deployment-env
echo export VITE_API_DRIVER=${VITE_API_DRIVER} | tee -a .deployment-env
echo export VITE_API_DUMMY_DELAY=${VITE_API_DUMMY_DELAY} | tee -a .deployment-env
echo export VITE_API_BASE_URL=${VITE_API_BASE_URL} | tee -a .deployment-env
echo export VITE_API_PROXY_TARGET=${VITE_API_PROXY_TARGET} | tee -a .deployment-env
echo export VITE_STRIPE_PUBLISHABLE_KEY=${VITE_STRIPE_PUBLISHABLE_KEY} | tee -a .deployment-env
echo export VITE_GOOGLE_MAPS_API_KEY=${VITE_GOOGLE_MAPS_API_KEY} | tee -a .deployment-env
echo export VITE_FIXR_URL=${VITE_FIXR_URL} | tee -a .deployment-env
echo export VITE_DEFAULT_SUPPORT_EMAIL=${VITE_DEFAULT_SUPPORT_EMAIL} | tee -a .deployment-env
echo export VITE_DEFAULT_SUPPORT_PHONE=${VITE_DEFAULT_SUPPORT_PHONE} | tee -a .deployment-env
echo export VITE_DEFAULT_SUPPORT_MEETING_URL=${VITE_DEFAULT_SUPPORT_MEETING_URL} | tee -a .deployment-env
echo export VITE_WATCHDOG_URL=${VITE_WATCHDOG_URL} | tee -a .deployment-env
echo export VITE_WATCHDOG_PLAYBACK_URL=${VITE_WATCHDOG_PLAYBACK_URL} | tee -a .deployment-env
echo export VITE_DROPBOX_APP_ID=${VITE_DROPBOX_APP_ID} | tee -a .deployment-env
echo export IMAGE_NAME=${IMAGE_NAME} | tee -a .deployment-env
echo export DEPLOYMENT_NAME=${DEPLOYMENT_NAME} | tee -a .deployment-env
echo export PROJECT_ID=${PROJECT_ID} | tee -a .deployment-env
echo export CLUSTER_NAME=${CLUSTER_NAME} | tee -a .deployment-env
echo export DEFAULT_ZONE=${DEFAULT_ZONE} | tee -a .deployment-env
echo export KEY_FILE=${KEY_FILE} | tee -a .deployment-env
echo export DOCKER_NAME=${DOCKER_NAME} | tee -a .deployment-env
echo export KUBERNETES_DEPLOYMENT_NAME=${KUBERNETES_DEPLOYMENT_NAME} | tee -a .deployment-env