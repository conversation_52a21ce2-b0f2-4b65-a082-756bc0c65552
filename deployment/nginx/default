server {
        listen 80 default_server;
        listen [::]:80 default_server;

        proxy_ignore_headers Vary;

        error_log  /var/log/nginx/error.log;
        access_log /var/log/nginx/access.log;

        root /var/www/html;

        index index.html index.htm index.nginx-debian.html;

        server_name _;

        # Proxy API requests
        location ${VITE_API_BASE_URL}/ {
                proxy_pass ${VITE_API_PROXY_TARGET}/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_ssl_server_name on;
        }

        location / {
                try_files /index.html =404;
        }

        location = /favicon.ico { access_log off; log_not_found off; }
        location = /robots.txt { access_log off; log_not_found off; }

        location ~* \.(?:jpg|jpeg|gif|png|ico|cur|gz|svg|svgz|mp4|ogg|ogv|webm|htc|svg|woff|woff2|ttf)$ {
                expires 1M;
                access_log off;
                error_log off;
                add_header Cache-Control "public";
        }

        location ~* \.(?:css|js)$ {
                expires 7d;
                access_log off;
                error_log off;
                add_header Cache-Control "public";
        }

        location ~ /\.ht {
                deny  all;
        }

        location ~* \.(?:xml|csv)$ {
                access_log off;
                error_log off;
        }
}