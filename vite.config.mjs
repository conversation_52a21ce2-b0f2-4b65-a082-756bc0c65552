import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import svgLoader from "vite-svg-loader";
import { sentryVitePlugin } from "@sentry/vite-plugin";
import { ENVIRONMENT_ENUM } from './src/sentry'
import { hash } from "./src/Utilities/Helpers";

// https://vitejs.dev/config/
export default ({ mode }) => {
    process.env = {...process.env, ...loadEnv(mode, process.cwd())}; // Load variables from .env

    const {
        VITE_SENTRY_ORG: sentryOrg,
        VITE_SENTRY_PROJECT: sentryProject,
        VITE_SENTRY_AUTH_TOKEN: sentryAuthToken,
        VITE_ENVIRONMENT: env,
        VITE_API_PROXY_TARGET: proxyTarget,
    } = process.env

    const plugins = [
        vue(),
        svgLoader()
    ]

    if (env === ENVIRONMENT_ENUM.PRODUCTION) {
        plugins.push(
            sentryVitePlugin({
                include: "./dist",
                ignore: ['node_modules'],
                org: sentryOrg,
                project: sentryProject,
                // Auth tokens can be obtained from https://sentry.io/settings/account/api/auth-tokens/
                // and needs the `project:releases` and `org:read` scopes
                authToken: sentryAuthToken,
            })
        )
    }

    return  defineConfig({
        build: {
            sourcemap: true,
            rollupOptions: {
                output: {
                    entryFileNames: `[name]` + hash + `.js`,
                    chunkFileNames: `[name]` + hash + `.js`,
                    assetFileNames: `[name]` + hash + `.[ext]`
                }
            }
        },
        plugins,
        resolve: {
            alias: {
                '@': fileURLToPath(new URL('./src', import.meta.url))
            }
        },
        server: {
            proxy: proxyTarget
                ? {
                    '/api': {
                        target: proxyTarget,
                        changeOrigin: true,
                        rewrite: path => path.replace(/^\/api/, ''),
                    }
                }
                : undefined
        }
    })
}