definitions:
  steps:
    - step: &configure-env
        name: Configure Env
        artifacts:
          - .deployment-env
        script:
          - echo "Configuring Env"
          - ./deployment/scripts/init-deployment-env.sh
    - step: &build-deployment
        name: Build Deployment
        image: node:20
        services:
          - docker
        artifacts:
          - .gc-deployment-*
        script:
          - export NODE_OPTIONS=--max_old_space_size=3072
          - echo "Starting Build"
          - source .deployment-env
          - sed -i -e "s|\${VITE_API_BASE_URL}|$VITE_API_BASE_URL|g" -e "s|\${VITE_API_PROXY_TARGET}|$VITE_API_PROXY_TARGET|g" deployment/nginx/default
          - ./deployment/scripts/init-app-env.sh
          - npm ci
          - npm run build
          - curl -sSL https://sdk.cloud.google.com | bash
          - export IMAGE_NAME="gcr.io/$PROJECT_ID/$IMAGE_NAME:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - echo $KEY_FILE | base64 -d > ~/.gcloud-api-key.json
          - source /root/google-cloud-sdk/path.bash.inc
          - export USE_GKE_GCLOUD_AUTH_PLUGIN=True
          - gcloud auth activate-service-account --key-file ~/.gcloud-api-key.json
          - gcloud config set project $PROJECT_ID
          - gcloud auth configure-docker
          - echo "Building Docker"
          - docker build -f $DOCKER_NAME -t $IMAGE_NAME .
          - docker push $IMAGE_NAME
          - |
            touch ".gc-deployment-$KUBERNETES_DEPLOYMENT_NAME"
            echo "$KUBERNETES_DEPLOYMENT_NAME~$IMAGE_NAME" >> ".gc-deployment-$KUBERNETES_DEPLOYMENT_NAME"
    - step: &deploy
        name: Deploying
        image: google/cloud-sdk:latest
        services:
          - docker
        script:
          - echo "Starting Deployment"
          - source .deployment-env
          - echo $KEY_FILE | base64 -d > ~/.gcloud-api-key.json
          - export USE_GKE_GCLOUD_AUTH_PLUGIN=True
          - gcloud auth activate-service-account --key-file ~/.gcloud-api-key.json
          - gcloud config set project $PROJECT_ID
          - gcloud auth configure-docker
          - echo "Updating Cluster Deployment"
          - gcloud container clusters get-credentials $CLUSTER_NAME --zone=$DEFAULT_ZONE --project $PROJECT_ID
          - |
            DEPLOYMENTS=(.gc-deployment-*)
            for i in ${!DEPLOYMENTS[@]}; do
                    if [ $i != 0  ]; then
                            echo -n "," >> .images
                            echo -n $(cat ${DEPLOYMENTS[$i]}) >> .images
                    else
                            touch .images
                            echo -n $(cat ${DEPLOYMENTS[$i]}) >> .images
                    fi
            done
          - |
            readarray -d "," -t IMAGES < .images
            for i in ${!IMAGES[@]}; do
                readarray -d "~" -t DEP <<< ${IMAGES[$i]}
                if [[ ${DEP[0]} =~ ^" "*$ ]]; then
                  echo "Empty"
                else
                  DEPLOYMENT="deployment/${DEP[0]}"
                  IMAGE=${DEP[1]}
                  echo "Deploying to: $DEPLOYMENT Image: $IMAGE"
                  kubectl set image $DEPLOYMENT server=$IMAGE
                fi
            done
          - echo "Deployed. Please check GCP for deployment status."
pipelines:
  custom:
    dev:
      - step:
          <<: *configure-env
          name: Configure Dev Environment
          deployment: development
      - step:
          <<: *build-deployment
          name: Build Dev Deployment
      - step:
          <<: *deploy
          name: Deploy Dev Deployment
  branches:
    development:
      - step:
          <<: *configure-env
          name: Configure Dev Environment
          deployment: development
      - step:
          <<: *build-deployment
          name: Build Dev Deployment
      - step:
          <<: *deploy
          name: Deploy Dev Deployment
  tags:
    'v*.*.*':
      - step:
          <<: *configure-env
          name: Configure Prod Environment
          deployment: production
      - step:
          <<: *build-deployment
          name: Build Prod Deployment
      - step:
          <<: *deploy
          name: Deploy Prod Deployment
          trigger: manual
